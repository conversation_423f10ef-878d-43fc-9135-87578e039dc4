
import { useState, useRef, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Mic, MicOff, Play, Pause, Download, Square, AlertCircle } from "lucide-react";
import { useTheme } from "next-themes";
import { toast } from "@/components/ui/sonner";
import WaveVisualizer from "./WaveVisualizer";
import Loading from "./Loading";
import Error from "./Error";

interface VoiceRecorderProps {
  className?: string;
}

/**
 * Komponen perekam suara untuk karaoke
 * - Rekam suara menggunakan MediaRecorder API
 * - Putar ulang rekaman
 * - Download rekaman sebagai file MP3/WAV
 * - Visualisasi gelombang suara real-time
 * - Error handling dan loading states
 * - Theme support
 */
const VoiceRecorder = ({ className }: VoiceRecorderProps) => {
  const { theme } = useTheme();
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exportFormat, setExportFormat] = useState<"webm" | "wav">("webm");

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const intervalRef = useRef<NodeJS.Timeout>();

  // Cleanup saat komponen unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  const startRecording = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check if MediaRecorder is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('MediaRecorder tidak didukung di browser ini');
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100,
          channelCount: 2
        }
      });

      streamRef.current = stream;
      chunksRef.current = [];

      // Choose the best available format
      let mimeType = 'audio/webm';
      if (!MediaRecorder.isTypeSupported('audio/webm')) {
        if (MediaRecorder.isTypeSupported('audio/mp4')) {
          mimeType = 'audio/mp4';
        } else if (MediaRecorder.isTypeSupported('audio/wav')) {
          mimeType = 'audio/wav';
        }
      }

      const mediaRecorder = new MediaRecorder(stream, { mimeType });
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: mimeType });
        setRecordedBlob(blob);

        // Create audio URL for playback
        if (audioUrl) {
          URL.revokeObjectURL(audioUrl);
        }
        const url = URL.createObjectURL(blob);
        setAudioUrl(url);

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
        toast.success("Rekaman berhasil disimpan!");
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        setError('Terjadi kesalahan saat merekam');
        toast.error("Gagal merekam audio");
      };

      mediaRecorder.start(100); // Collect data every 100ms
      setIsRecording(true);
      setRecordingTime(0);

      // Start timer
      intervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

      toast.success("Mulai merekam...");

    } catch (error: any) {
      console.error('Error starting recording:', error);
      let errorMessage = 'Tidak dapat mengakses mikrofon';

      if (error.name === 'NotAllowedError') {
        errorMessage = 'Akses mikrofon ditolak. Silakan berikan izin akses mikrofon.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'Mikrofon tidak ditemukan. Pastikan mikrofon terhubung.';
      } else if (error.name === 'NotSupportedError') {
        errorMessage = 'Browser tidak mendukung perekaman audio.';
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
  };

  const playRecording = () => {
    if (audioUrl && !isPlaying) {
      const audio = new Audio(audioUrl);
      audioRef.current = audio;
      
      audio.onended = () => {
        setIsPlaying(false);
      };
      
      audio.play();
      setIsPlaying(true);
    }
  };

  const stopPlayback = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
    }
  };

  const convertToWav = async (blob: Blob): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const fileReader = new FileReader();

      fileReader.onload = async () => {
        try {
          const arrayBuffer = fileReader.result as ArrayBuffer;
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

          // Convert to WAV
          const length = audioBuffer.length;
          const numberOfChannels = audioBuffer.numberOfChannels;
          const sampleRate = audioBuffer.sampleRate;
          const buffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
          const view = new DataView(buffer);

          // WAV header
          const writeString = (offset: number, string: string) => {
            for (let i = 0; i < string.length; i++) {
              view.setUint8(offset + i, string.charCodeAt(i));
            }
          };

          writeString(0, 'RIFF');
          view.setUint32(4, 36 + length * numberOfChannels * 2, true);
          writeString(8, 'WAVE');
          writeString(12, 'fmt ');
          view.setUint32(16, 16, true);
          view.setUint16(20, 1, true);
          view.setUint16(22, numberOfChannels, true);
          view.setUint32(24, sampleRate, true);
          view.setUint32(28, sampleRate * numberOfChannels * 2, true);
          view.setUint16(32, numberOfChannels * 2, true);
          view.setUint16(34, 16, true);
          writeString(36, 'data');
          view.setUint32(40, length * numberOfChannels * 2, true);

          // Convert audio data
          let offset = 44;
          for (let i = 0; i < length; i++) {
            for (let channel = 0; channel < numberOfChannels; channel++) {
              const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
              view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
              offset += 2;
            }
          }

          resolve(new Blob([buffer], { type: 'audio/wav' }));
        } catch (error) {
          reject(error);
        }
      };

      fileReader.onerror = () => reject(new Error('Failed to read audio file'));
      fileReader.readAsArrayBuffer(blob);
    });
  };

  const downloadRecording = async () => {
    if (!recordedBlob) return;

    try {
      setIsLoading(true);
      let downloadBlob = recordedBlob;
      let extension = 'webm';

      if (exportFormat === 'wav') {
        downloadBlob = await convertToWav(recordedBlob);
        extension = 'wav';
      }

      const url = URL.createObjectURL(downloadBlob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `karaoke-recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.${extension}`;
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success(`Rekaman berhasil diunduh sebagai ${extension.toUpperCase()}`);
    } catch (error) {
      console.error('Error downloading recording:', error);
      toast.error('Gagal mengunduh rekaman');
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (error) {
    return (
      <Card className={`backdrop-blur-md ${className}`}>
        <CardContent className="p-6">
          <Error
            title="Error Perekaman"
            message={error}
            onRetry={() => {
              setError(null);
              startRecording();
            }}
            variant="minimal"
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`backdrop-blur-md ${className}`}>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Mic className="w-5 h-5" />
          Rekam Suara
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Visualizer */}
        <WaveVisualizer
          isRecording={isRecording}
          stream={streamRef.current}
        />

        {/* Recording Timer */}
        <div className="text-center">
          <div className="text-2xl font-mono">
            {formatTime(recordingTime)}
          </div>
          {isRecording && (
            <div className="flex items-center justify-center gap-2 mt-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-sm opacity-70">
                Merekam...
              </span>
            </div>
          )}
        </div>

        {/* Recording Controls */}
        <div className="flex items-center justify-center gap-3">
          {!isRecording ? (
            <Button
              onClick={startRecording}
              disabled={isLoading}
              className="bg-red-500 hover:bg-red-600 text-white"
              size="lg"
            >
              {isLoading ? (
                <Loading size="sm" text="" className="mr-2" />
              ) : (
                <Mic className="w-5 h-5 mr-2" />
              )}
              {isLoading ? "Mempersiapkan..." : "Mulai Rekam"}
            </Button>
          ) : (
            <Button
              onClick={stopRecording}
              variant="outline"
              size="lg"
            >
              <Square className="w-5 h-5 mr-2" />
              Stop
            </Button>
          )}
        </div>

        {/* Playback Controls */}
        {recordedBlob && (
          <div className="space-y-4 pt-4 border-t border-border">
            <div className="text-center text-sm opacity-70">
              Rekaman tersimpan
            </div>

            {/* Export Format Selection */}
            <div className="flex items-center justify-center gap-2">
              <span className="text-sm opacity-70">Format:</span>
              <Select value={exportFormat} onValueChange={(value: "webm" | "wav") => setExportFormat(value)}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="webm">WebM</SelectItem>
                  <SelectItem value="wav">WAV</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-center gap-3 flex-wrap">
              {!isPlaying ? (
                <Button
                  onClick={playRecording}
                  variant="outline"
                  size="sm"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Putar
                </Button>
              ) : (
                <Button
                  onClick={stopPlayback}
                  variant="outline"
                  size="sm"
                >
                  <Pause className="w-4 h-4 mr-2" />
                  Stop
                </Button>
              )}

              <Button
                onClick={downloadRecording}
                disabled={isLoading}
                variant="outline"
                size="sm"
              >
                {isLoading ? (
                  <Loading size="sm" text="" className="mr-2" />
                ) : (
                  <Download className="w-4 h-4 mr-2" />
                )}
                {isLoading ? "Mengkonversi..." : `Download ${exportFormat.toUpperCase()}`}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VoiceRecorder;
